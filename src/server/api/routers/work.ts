import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "../trpc";
import { work } from "~/server/db/schema";
import { TRPCError } from "@trpc/server";
import { success } from "zod/v4";

const workRouter = createTRPCRouter({
  append: publicProcedure.input(
    z
      .object({
        name: z.string(),
        description: z.string().optional(),
        dueAt: z.date().refine((date) => date > new Date(), {
          message: "Due date must be in the future",
        }),
        required: z.boolean(),
        weight: z.number().optional(),
        link: z.string().optional(),
      })
      .mutation(async ({ ctx, input }) => {
        await ctx.db.insert(work).values({
          name: input.name,
          description: input.description,
          dueAt: input.dueAt,
          required: input.required,
          weight: input.weight,
          link: input.link,
          completed: false,
        });
        return { success };
      }),
  ),
});

export default workRouter;
