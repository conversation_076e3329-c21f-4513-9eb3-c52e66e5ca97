// Example model schema from the Drizzle docs
// https://orm.drizzle.team/docs/sql-schema-declaration

import { sql } from "drizzle-orm";
import { index, pgTableCreator } from "drizzle-orm/pg-core";

/**
 * This is an example of how to use the multi-project schema feature of Drizzle ORM. Use the same
 * database instance for multiple projects.
 *
 * @see https://orm.drizzle.team/docs/goodies#multi-project-schema
 */
const createTable = pgTableCreator((name) => `cooperator_${name}`);

const work = createTable(
  "work",
  (d) => ({
    id: d.integer().primaryKey().generatedByDefaultAsIdentity(),
    name: d.varchar({ length: 256 }).notNull(),
    createdAt: d
      .timestamp({ withTimezone: true })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    dueAt: d.timestamp({ withTimezone: true }).notNull(),
    completeAt: d.timestamp({ withTimezone: true }),
    required: d.boolean().notNull(),
    completed: d.boolean().notNull(),
    weight: d.numeric(),
    score: d.integer(),
    description: d.text(),
    link: d.text(),
  }),
  (t) => [index("name_idx").on(t.name)],
);

export { createTable, work };
